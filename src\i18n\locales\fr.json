{"messages": {"success": "Opération effectuée avec succès !", "error": "Une erreur s'est produite", "loading": "Chargement...", "notFound": "Non trouvé", "not_found": "Utilisateur non trouvé dans le système des affaires sociales", "found": "Utilisateur trouvé avec bon de commande disponible", "no_purchase_order": "Utilisateur trouvé mais aucun bon de commande disponible pour son gouvernorat", "undefined": "Remplir le (s) champs obligatoire (s)", "Trip_already_exists": "<PERSON><PERSON><PERSON> existant", "Failed_delete_trip": "Impossible de supprimer un trajet utilisé", "paidSubscriptionEditWarning": "Attention: La modification d'un abonnement payé entraînera l'annulation de la transaction existante et la création d'une nouvelle transaction."}, "components": {"sellsPeriodClosed": {"noOpenPeriod": "Au<PERSON>ne période de vente ouverte pour le moment !", "reopening": "Notre espace de souscription réouvre dans :", "days": "jours", "description": "Vous ne pouvez pas créer de nouveaux abonnements pour le moment car il n'y a pas de période de vente active.", "nextOpening": "Prochaine ouverture", "contactAdmin": "Veuillez contacter un administrateur pour plus d'informations."}}, "permissions": {"manage_duplicata": "Gestion des duplicats", "view_duplicata": "Voir duplicats", "create_duplicata": "<PERSON><PERSON><PERSON> dup<PERSON>", "edit_duplicata": "Modifier duplicats", "delete_duplicata": "Supprimer duplicats", "manage_remise_except": "Gestion des remises exceptionnels", "view_remise_except": "Voir remises exceptionnels", "create_remise_except": "<PERSON><PERSON><PERSON> remises exceptionnels", "edit_remise_except": "Modifier remises exceptionnels", "delete_remise_except": "Supprimer remises exceptionnels", "manage_carte_impersonnels": "Gestion des cartes impersonnels", "view_carte_impersonnels": "Voir cartes impersonnels", "create_carte_impersonnels": "<PERSON><PERSON>er cartes impersonnels", "edit_carte_impersonnels": "Modifier cartes impersonnels", "delete_carte_impersonnels": "Supprimer cartes impersonnels", "manage_abn_impersonnels": "Gestion d'abonnement impersonnels ", "view_abn_impersonnels": "Voir d'abonnement impersonnels", "create_abn_impersonnels": "<PERSON><PERSON><PERSON> d'abonnement impersonnels", "edit_abn_impersonnels": "Modifier d'abonnement impersonnels", "delete_abn_impersonnels": "Supprimer d'abonnement impersonnels", "manage_abn_convention_impersonnels": "Gestion d'abonnement convention impersonnels", "view_abn_convention_impersonnels": "Voir d'abonnement convention impersonnels", "create_abn_convention_impersonnels": "Créer d'abonnement convention impersonnels", "edit_abn_convention_impersonnels": "Modifier d'abonnement convention impersonnels", "delete_abn_convention_impersonnels": "Supprimer d'abonnement convention impersonnels", "manage_abn_trajet": "Gestion d'abonnement : trajet", "view_abn_trajet": "Voir d'abonnement : trajet", "create_abn_trajet": "<PERSON><PERSON><PERSON> d'abonnement : trajet", "edit_abn_trajet": "Modifier d'abonnement : trajet", "delete_abn_trajet": "Supprimer d'abonnement : trajet", "manage_abn_establishments": "Gestion d'abonnement : Etablissement", "view_abn_establishments": "Voir d'abonnement : Etablissement", "create_abn_establishments": "<PERSON><PERSON>er d'abonnement : Etablissement", "edit_abn_establishments": "Modifier d'abonnement : Etablissement", "delete_abn_establishments": "Supprimer d'abonnement : Etablissement", "manage_abn_photos_firstname_lastname": "Gestion d'abonnement : nom , prenom et photo", "view_abn_photos_firstname_lastname": "Voir d'abonnement : nom , prenom et photo", "create_abn_photos_firstname_lastname": "<PERSON><PERSON>er d'abonnement : nom , prenom et photo", "edit_abn_photos_firstname_lastname": "Modifier d'abonnement : nom , prenom et photo", "delete_abn_photos_firstname_lastname": "Supprimer d'abonnement : nom , prenom et photo", "manage_reimpression": "Gestion reimpression", "view_reimpression": "Voir reimpression", "create_reimpression": "C<PERSON>er reimpression", "edit_reimpression": "Modifier reimpression", "delete_reimpression": "Supprimer reimpression", "dashboard": "Tableau de bord", "manage_dashboard": "<PERSON><PERSON><PERSON> tableau de bord", "view_dashboard": "Voir tableau de bord", "create_dashboard": "<PERSON><PERSON><PERSON> tableau de bord", "edit_dashboard": "Modifier tableau de bord", "delete_dashboard": "Supprimer tableau de bord", "roles_permissions": "Rôles et permissions", "manage_roles_permissions": "<PERSON><PERSON><PERSON> rôles et permissions", "view_roles_permissions": "Voir rôles et permissions", "create_roles_permissions": "<PERSON><PERSON><PERSON> rôles et permissions", "edit_roles_permissions": "Modifier rôles et permissions", "delete_roles_permissions": "Supprimer rôles et permissions", "admins": "Administrateurs", "manage_admins": "<PERSON><PERSON><PERSON> administrateurs", "view_admins": "Voir administrateurs", "create_admins": "<PERSON><PERSON><PERSON> administrateurs", "edit_admins": "Modifier administrateurs", "delete_admins": "Supprimer administrateurs", "governorates": "Gouvernorats", "manage_governorates": "<PERSON><PERSON><PERSON> gouve<PERSON>", "view_governorates": "Voir gouvernorats", "create_governorates": "<PERSON><PERSON><PERSON> gouverno<PERSON>", "edit_governorates": "Modifier gouvernorats", "delete_governorates": "Supprimer gouvernorats", "delegations": "Délégations", "manage_delegations": "<PERSON><PERSON><PERSON> dé<PERSON>s", "view_delegations": "Voir délégations", "create_delegations": "<PERSON><PERSON>er dé<PERSON>s", "edit_delegations": "Modifier délégations", "delete_delegations": "Supprimer délégations", "establishment_types": "Types d'établissements", "manage_establishment_types": "Gérer types d'établissements", "view_establishment_types": "Voir types d'établissements", "create_establishment_types": "Créer types d'établissements", "edit_establishment_types": "Modifier types d'établissements", "delete_establishment_types": "Supprimer types d'établissements", "school_degrees": "Niveaux scolaires", "manage_school_degrees": "G<PERSON>rer niveaux scolaires", "view_school_degrees": "Voir niveaux scolaires", "create_school_degrees": "Créer niveaux scolaires", "edit_school_degrees": "Modifier niveaux scolaires", "delete_school_degrees": "Supprimer niveaux scolaires", "establishments": "Établissements", "manage_establishments": "<PERSON><PERSON>rer établissements", "view_establishments": "Voir établissements", "create_establishments": "Créer établissements", "edit_establishments": "Modifier établissements", "delete_establishments": "Supprimer établissements", "seasons": "Saisons", "manage_seasons": "<PERSON><PERSON><PERSON> saisons", "view_seasons": "Voir saisons", "create_seasons": "<PERSON><PERSON>er saisons", "edit_seasons": "Modifier saisons", "delete_seasons": "Supprimer saisons", "stations": "Stations", "manage_stations": "Gérer stations", "view_stations": "Voir stations", "create_stations": "Créer stations", "edit_stations": "Modifier stations", "delete_stations": "Supprimer stations", "routes": "Trajets", "manage_routes": "<PERSON><PERSON><PERSON> trajets", "view_routes": "Voir trajets", "create_routes": "<PERSON><PERSON><PERSON> trajets", "edit_routes": "Modifier trajets", "delete_routes": "Supprimer trajets", "lines": "<PERSON><PERSON><PERSON>", "manage_lines": "<PERSON><PERSON><PERSON> lignes", "view_lines": "Voir lignes", "create_lines": "<PERSON><PERSON><PERSON> lignes", "edit_lines": "Modifier lignes", "delete_lines": "Supprimer lignes", "type_vehicules": "Types de véhicules", "manage_type_vehicules": "Gérer types de véhicules", "view_type_vehicules": "Voir types de véhicules", "create_type_vehicules": "Créer types de véhicules", "edit_type_vehicules": "Modifier types de véhicules", "delete_type_vehicules": "Supprimer types de véhicules", "location_types": "Types de location", "manage_location_types": "Gérer types de location", "view_location_types": "Voir types de location", "create_location_types": "C<PERSON>er types de location", "edit_location_types": "Modifier types de location", "delete_location_types": "Supprimer types de location", "location_seasons": "Saisons de location", "manage_location_seasons": "Gérer saisons de location", "view_location_seasons": "Voir saisons de location", "create_location_seasons": "Créer saisons de location", "edit_location_seasons": "Modifier saisons de location", "delete_location_seasons": "Supprimer saisons de location", "vehicle_season_pricing": "Tarification des véhicules par saison", "manage_vehicle_season_pricing": "<PERSON><PERSON>rer tarification des véhicules par saison", "view_vehicle_season_pricing": "Voir tarification des véhicules par saison", "create_vehicle_season_pricing": "<PERSON>réer tarification des véhicules par saison", "edit_vehicle_season_pricing": "Modifier tarification des véhicules par saison", "delete_vehicle_season_pricing": "Supprimer tarification des véhicules par saison", "type_vehicle_type_locations": "Types de véhicules par location", "manage_type_vehicle_type_locations": "Gérer types de véhicules par location", "view_type_vehicle_type_locations": "Voir types de véhicules par location", "create_type_vehicle_type_locations": "Créer types de véhicules par location", "edit_type_vehicle_type_locations": "Modifier types de véhicules par location", "delete_type_vehicle_type_locations": "Supprimer types de véhicules par location", "campaigns": "Campagnes", "manage_campaigns": "<PERSON><PERSON><PERSON>", "view_campaigns": "Voir campagnes", "create_campaigns": "<PERSON><PERSON><PERSON> camp<PERSON>", "edit_campaigns": "Modifier campagnes", "delete_campaigns": "Supp<PERSON>er campagnes", "sales_periods": "Périodes de vente", "manage_sales_periods": "<PERSON><PERSON><PERSON> p<PERSON> de vente", "view_sales_periods": "Voir périodes de vente", "create_sales_periods": "<PERSON><PERSON><PERSON> p<PERSON> de vente", "edit_sales_periods": "Modifier périodes de vente", "delete_sales_periods": "Supp<PERSON>er périodes de vente", "periodicities": "Périodicités", "manage_periodicities": "<PERSON><PERSON><PERSON> périodic<PERSON>s", "view_periodicities": "Voir périodicités", "create_periodicities": "<PERSON><PERSON>er périodicités", "edit_periodicities": "Modifier périodicités", "delete_periodicities": "Supprimer périodicités", "agencies": "Agences", "manage_agencies": "<PERSON><PERSON><PERSON> age<PERSON>", "view_agencies": "Voir agences", "create_agencies": "<PERSON><PERSON><PERSON> agences", "edit_agencies": "Modifier agences", "delete_agencies": "Supprimer agences", "sales_points": "Points de vente", "manage_sales_points": "<PERSON><PERSON>rer points de vente", "view_sales_points": "Voir points de vente", "create_sales_points": "<PERSON><PERSON><PERSON> points de vente", "edit_sales_points": "Modifier points de vente", "delete_sales_points": "Supprimer points de vente", "assign_agents": "Affectation des agents", "manage_assign_agents": "Gérer affectation des agents", "view_assign_agents": "Voir affectation des agents", "create_assign_agents": "Créer affectation des agents", "edit_assign_agents": "Modifier affectation des agents", "delete_assign_agents": "Supprimer affectation des agents", "stock_cards": "Stock des cartes", "manage_stock_cards": "Gérer stock des cartes", "view_stock_cards": "Voir stock des cartes", "create_stock_cards": "Créer stock des cartes", "edit_stock_cards": "Modifier stock des cartes", "delete_stock_cards": "Supprimer stock des cartes", "card_types": "Types de cartes", "manage_card_types": "Gérer types de cartes", "view_card_types": "Voir types de cartes", "create_card_types": "Créer types de cartes", "edit_card_types": "Modifier types de cartes", "delete_card_types": "Supprimer types de cartes", "abn_types": "Types d'abonnements", "manage_abn_types": "Gérer types d'abonnements", "view_abn_types": "Voir types d'abonnements", "create_abn_types": "Créer types d'abonnements", "edit_abn_types": "Modifier types d'abonnements", "delete_abn_types": "Supprimer types d'abonnements", "payment_methods": "Méthodes de paiement", "manage_payment_methods": "<PERSON><PERSON>rer méthodes de paiement", "view_payment_methods": "Voir méthodes de paiement", "create_payment_methods": "<PERSON>réer méthodes de paiement", "edit_payment_methods": "Modifier méthodes de paiement", "delete_payment_methods": "Supprimer méthodes de paiement", "duplicate_motifs": "Motifs de duplication", "manage_duplicate_motifs": "G<PERSON>rer motifs de duplication", "view_duplicate_motifs": "Voir motifs de duplication", "create_duplicate_motifs": "Créer motifs de duplication", "edit_duplicate_motifs": "Modifier motifs de duplication", "delete_duplicate_motifs": "Supprimer motifs de duplication", "tariff_bases": "Bases tarifaires", "manage_tariff_bases": "Gérer bases tarifaires", "view_tariff_bases": "Voir bases tarifaires", "create_tariff_bases": "Créer bases tarifaires", "edit_tariff_bases": "Modifier bases tarifaires", "delete_tariff_bases": "Supprimer bases tarifaires", "cards_fees": "Frais de cartes", "manage_cards_fees": "<PERSON><PERSON><PERSON> frais de cartes", "view_cards_fees": "Voir frais de cartes", "create_cards_fees": "<PERSON><PERSON><PERSON> frais de cartes", "edit_cards_fees": "Modifier frais de cartes", "delete_cards_fees": "Supprimer frais de cartes", "discounts": "Remises", "manage_discounts": "<PERSON><PERSON><PERSON> remises", "view_discounts": "Voir remises", "create_discounts": "<PERSON><PERSON><PERSON> remises", "edit_discounts": "Modifier remises", "delete_discounts": "Supprimer remises", "client_types": "Types de clients", "manage_client_types": "Gérer types de clients", "view_client_types": "Voir types de clients", "create_client_types": "<PERSON><PERSON>er types de clients", "edit_client_types": "Modifier types de clients", "delete_client_types": "Supprimer types de clients", "clients": "Clients", "manage_clients": "Gérer clients", "view_clients": "Voir clients", "create_clients": "Créer clients", "edit_clients": "Modifier clients", "delete_clients": "Supprimer clients"}, "dashboard": {"Statistiques_A1": "Nombre des abonnés par saision", "Statistiques_A2": "Classification des abonnés", "active_plans": "Total des abonnés scolaires", "expiring": "Total des abonnés conventionnés", "revenue": "<PERSON>en<PERSON>", "total_subscriptions": "Total des abonnements", "total_clients": "Total des clients", "monthly": "<PERSON><PERSON><PERSON>", "pending_subscriptions": "Abonnements en attente", "paid_subscriptions": "Abonnements payés", "summary": "Résumé", "subscriptions": "Abonnements", "financial": "Finances", "cards": "<PERSON><PERSON>", "clients": "Clients", "trips": "Trajets", "agents": "Agents", "date_range": "Période", "group_by": "Grouper par", "total": "Total", "day": "Jour", "week": "<PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON>", "year": "<PERSON><PERSON>", "loading": "Chargement...", "unknown": "Inconnu", "paid": "<PERSON><PERSON>", "not_paid": "Non payé", "canceled": "<PERSON><PERSON><PERSON>", "pending": "En attente", "subscription_by_status": "Abonnements par statut", "subscription_by_type": "Abonnements par type", "subscription_by_periodicity": "Abonnements par périodicité", "subscription_by_client_type": "Abonnements par type de client", "subscription_by_governorate": "Abonnements par gouvernorat", "subscriptions_over_time": "Évolution des abonnements", "revenue_over_time": "Évolution des revenus", "revenue_by_payment_method": "Revenus par méthode de paiement", "revenue_by_subscription_type": "Revenus par type d'abonnement", "revenue_by_sale_point": "Revenus par point de vente", "revenue_by_governorate": "Revenus par gouvernorat", "cards_by_type": "Cartes par type", "cards_by_motif": "Cartes par motif", "cards_by_sale_point": "Cartes par point de vente", "card_stock_status": "État du stock de cartes", "new_clients_over_time": "Évolution des nouveaux clients", "clients_by_type": "Clients par type", "clients_by_governorate": "Clients par gouvernorat", "popular_trips": "Trajets populaires", "popular_lines": "Lignes populaires", "station_usage": "Utilisation des stations", "subscriptions_by_agent": "Abonnements par agent", "revenue_by_agent": "Revenus par agent", "cards_by_agent": "Cartes par agent", "reset_filters": "Réinitialiser les filtres", "filters_reset": "Filtres réinitialisés"}, "login": {"welcome": "Bienvenue !", "subtitle": "Connectez-vous à votre compte", "tooltip": "Choisissez votre méthode de connexion préférée", "email": "E-mail", "phone": "Téléphone", "cin": "Carte ID", "password": "Mot de passe", "signIn": "Se connecter", "forgotPassword": "Mot de passe oublié ?", "or": "OU", "noAccount": "Vous n'avez pas de compte ?", "signUp": "Ins<PERSON>rivez-vous maintenant", "validation": {"required": "Ce champ est obligatoire", "email": "Veuillez entrer une adresse e-mail valide", "phone": "Le numéro de téléphone doit contenir 8 chiffres", "cin": "Le CIN doit contenir 8 chiffres"}, "placeholder": {"email": "Entrez votre e-mail", "phone": "Entrez votre numéro de téléphone", "cin": "Entrez votre CIN", "password": "Entrez votre mot de passe", "captcha": "<PERSON><PERSON><PERSON> le captcha"}}, "register": {"welcome": "Bienvenue !", "step1Title": "Informations personnelles", "step2Title": "Vérification du téléphone", "step3Title": "<PERSON><PERSON><PERSON>", "step4Title": "<PERSON>é<PERSON> du compte", "step5Title": "Vérification de l'email", "step6Title": "Termes et Conditions", "subtitle": "C<PERSON>ez un compte pour commencer.", "tooltip": "Saisissez vos informations avec soin.", "placeholder": {"email": "Adresse e-mail", "password": "Mot de passe", "confirmPassword": "Confirmez le mot de passe", "nom": "Nom", "prenom": "Prénom", "phone": "Numéro de téléphone", "adresse": "<PERSON><PERSON><PERSON>", "cin": "CIN"}, "validation": {"required": "Ce champ est obligatoire.", "email": "Veuillez entrer une adresse e-mail valide", "phone": "Le numéro de téléphone doit contenir 8 chiffres", "cin": "Le CIN doit contenir 8 chiffres", "passwordMismatch": "Les mots de passe ne correspondent pas.", "acceptTerms": "<PERSON><PERSON> de<PERSON> accepter les termes et conditions."}, "acceptTerms": "J'accepte les", "termsLink": "termes et conditions", "next": "Suivant", "previous": "Précédent", "signUp": "S'inscrire", "or": "ou", "haveAccount": "Vous avez déjà un compte?", "signIn": "Se connecter", "resendCode": "Renvoi du code", "phoneValidated": "Numéro de téléphone validé", "emailValidated": "E-mail validé", "retryIn": "<PERSON><PERSON><PERSON><PERSON> dans", "seconds": "secondes", "OTPPhoneTitle": "Entrez le code OTP", "OTPPhoneDescription": "Veuillez entrer le code OTP envoyé à votre numéro de téléphone", "OTPEmailTitle": "Entrez le code OTP", "OTPEmailDescription": "Veuillez entrer le code OTP envoyé à votre adresse e-mail"}, "reset": {"step1Title": "Saisissez votre e-mail", "step2Title": "Entrez le code", "step3Title": "Réinitialisez votre mot de passe", "requestTitle": "Réinitialisation du mot de passe", "requestDescription": "Saisissez les informations nécessaires pour réinitialiser votre mot de passe.", "emailPlaceholder": "Adresse e-mail", "newPasswordTitle": "Nouveau mot de passe", "newPasswordDescription": "Veuillez entrer et confirmer votre nouveau mot de passe.", "passwordPlaceholder": "Nouveau mot de passe", "confirmPlaceholder": "Confirmez le mot de passe", "resendCode": "Renvoyer le code", "retryIn": "<PERSON><PERSON><PERSON><PERSON> dans", "seconds": "secondes", "haveAccount": "Vous avez déjà un compte ?", "signIn": "Se connecter", "next": "Suivant", "previous": "Précédent", "confirm": "Confirmer", "validation": {"emailRequired": "Veuillez saisir votre adresse e-mail.", "emailValid": "<PERSON><PERSON><PERSON>z entrer une adresse e-mail valide.", "invalidOtp": "Code OTP invalide. Veuillez réessayer.", "passwordRequired": "Le mot de passe est requis.", "passwordLength": "Le mot de passe doit comporter au moins 8 caractères.", "confirmRequired": "Veuillez confirmer votre mot de passe.", "passwordMatch": "Les mots de passe ne correspondent pas."}, "EmailValidated": "E-mail validé avec succès !", "OTPEmailTitle": "Entrez le code de confirmation", "OTPEmailDescription": "Nous avons envoyé un code à votre email. Veuillez le saisir ci-dessous pour vérifier votre email."}, "access_denied": {"title": "<PERSON><PERSON><PERSON>", "sub_title": "<PERSON><PERSON><PERSON><PERSON>, vous n'avez pas l'autorisation d'accéder à cette page.", "button": "Retour à l'accueil"}, "not_found": {"title": "Page Introuvable", "sub_title": "<PERSON><PERSON><PERSON><PERSON>, la page que vous avez visitée n'existe pas.", "button": "Retour à l'accueil"}, "auth_header": {"profile": "Profil", "settings": "Paramètres", "logout": "Déconnexion"}, "auth_sidebar": {"dashboard": "Tableau de bord", "categories": {"dashboard": "Tableau de bord", "security": "Sécurité", "configs": "Paramètrages", "subscriptions": "Abonnements", "clients": "Clients", "location": "Localisation", "education": "Éducation", "transport": "Transport", "sales": "<PERSON><PERSON><PERSON>", "pricing": "Tarification", "busLocations": "Location bus", "manage_stats": "Statistiques", "advanced_stats": "Statistiques avancées"}, "roles_permissions": "Rôles et permissions", "manage_admins": "Utilisateurs", "manage_clients": "Clients", "manage_stockCards": "Stock cartes", "manage_cardTypes": "Types cartes", "manage_cardsFees": "Frais des cartes", "manage_governorates": "Gouvernorats", "manage_delegations": "Délégations", "manage_stations": "Stations", "manage_routes": "Trajets", "manage_lines": "<PERSON><PERSON><PERSON>", "manage_establishmentTypes": "Types d'établissements", "manage_establishment": "Établissements", "manage_abnTypes": "Types abonnements", "manage_paymentMethods": "Méthodes de paiments", "manage_duplicateMotifs": "Motifs de duplication", "manage_campaignTypes": "Types compagnes", "manage_campaigns": "Compagnes", "manage_salesPeriods": "Périodes de ventes", "manage_agencies": "Agences", "manage_salesPoints": "Points de ventes", "manage_assignAgents": "Affectation des agents", "manage_schoolDegrees": "Niveaux scolaires", "manage_academicYears": "Années Académiques", "manage_abnPeriods": "Periodicité", "manage_newSubs": "Abonnements", "manage_clientTypes": "Types clients", "manage_discounts": "Remise", "manage_tariffBases": "Bases tarifaires", "manage_seasons": "Saisons", "manage_location_seasons": "Saisons de location", "manage_vehicle_season_pricing": "Tarification location", "manage_typeVehicules": "Types véhicules", "manage_locationTypes": "Types location", "manage_typeVehicleTypeLocations": "Véhicules par location", "manage_configs": "Configurations système"}, "manage_tariffBase": {"title": "Bases tarifaires", "add": "Ajouter un tarif", "edit": "Modifier le tarif", "details": "<PERSON><PERSON><PERSON> du tarif", "save": "Enregistrer", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce tarif ?", "confirmAction": "Confirmation", "confirmAdd": "Voulez-vous vraiment ajouter ce tarif ?", "confirmUpdate": "Voulez-vous vraiment mettre à jour ce tarif ?", "yes": "O<PERSON>", "no": "Non", "labels": {"id": "Id", "abn_type": "Type d'abonnement", "forWebsite": "Pour le site web", "types": "Types", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions", "name": "Désignation", "name_fr": "Désignation (Français)", "name_en": "Désignation (Anglais)", "name_ar": "Désignation (Arabe)", "tariffPerKM": "<PERSON><PERSON><PERSON> par <PERSON>", "date": "Date d'application"}, "placeholders": {"id": "Entrez l'identifiant", "abn_type": "Sélectionnez le type d'abonnement", "name_fr": "Entrez le nom en français", "name_en": "Entrez le nom en anglais", "name_ar": "Entrez le nom en arabe", "tariffPerKM": "Entrez le tarif par kilomètre", "date": "Sélectionnez la date d'application"}, "errors": {"idRequired": "L'identifiant est requis", "abnTypeRequired": "Le type d'abonnement est requis", "nameRequired": "Le nom est requis", "tariffPerKMRequired": "Le tarif par kilomètre est requis", "dateRequired": "La date d'application est requise"}, "filters": {"id": "Filtrer par identifiant", "abn_types": "Filtrer par type d'abonnement", "name": "Filtrer par nom", "date": "Filtrer par date d'application"}}, "manage_users": {"selectGovernorate": "Sélectionnez un gouvernorat", "types": {"university": "universitaire", "school": "scolaire", "civil": "civil"}, "placeholders": {"dob": "Sélectionnez la date de naissance", "governorate": "Sélectionner un gouvernorat", "delegation": "Sélectionner une délégation"}, "client": {"title_client": "Clients", "add_client": "Ajouter un client", "edit_client": "Modifier un client", "details_client": "Détails de l'client", "placeholders": {"lastname": "Nom de l'client", "firstname": "Prénom de l'client", "dob": "Date de naissance", "governorate": "Gouvernorat de l'client", "delegation": "Delegation de l'client", "establishment": "Etablissement de l'client", "schoolDegree": "niveau scolaire de l'client", "phone": "Téléphone", "cin": "Entrer cin", "idImpersonal": "Entrer raison social/MF", "idConventional": "Entrer num convention", "matricule": "identifiant", "address": "<PERSON><PERSON><PERSON> complè<PERSON> de l'client", "addressImpersonal": "Adresse complète de la societé", "legalRepresentative": "Entrer le représentant légal", "societyName": "Entrer le nom de la société", "fax": "Entrer le fax", "email": "Adresse électronique de l'client", "emailImpersonal": "Adresse électronique de la société", "password": "Mot de passe de client", "clientType": "Type de client"}, "confirmAction": "Confirmation", "confirmAdd": "Voulez-vous vraiment ajouter ce client ?", "confirmUpdate": "Voulez-vous vraiment mettre à jour ce client ?", "yes": "O<PERSON>", "no": "Non", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce client ?", "deleteSuccess": "Client supprimé avec succès !", "defaultPasswordMessage": "Un mot de passe sera automatiquement généré et envoyé à l'adresse e-mail de l'client."}, "admin": {"title": "Gestion des utilisateurs", "add": "Ajouter un utilisateur", "edit": "Modifier un utilisateur", "details": "Détails de l'utilisateur", "defaultPasswordMessage": "Le mot de passe par défaut est 'admin'.", "placeholders": {"lastname": "Nom de l'utilisateur", "firstname": "Prénom de l'utilisateur", "phone": "Téléphone", "cin": "Identifiant", "address": "<PERSON><PERSON><PERSON> complète de l'utilisateur", "email": "Email de l'utilisateur", "password": "Mot de passe de l'utilisateur"}, "confirmAction": "Confirmation", "confirmAdd": "Voulez-vous vraiment ajouter cet utilisateur ?", "confirmUpdate": "Voulez-vous vraiment mettre à jour cet utilisateur ?", "yes": "O<PERSON>", "no": "Non", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cet utilisateur ?", "deleteSuccess": "utilisateur supprimé avec succès !"}, "labels": {"id": "Id", "lastname": "Nom", "firstname": "Prénom", "dob": "Date de naissance", "identityNumber": "Identifiant", "conventionNumber": "Convention", "legalRepresentative": "Représentant légal", "societyName": "Nom de la société", "fax": "Fax", "withTVA": "Avec TVA", "phone": "Téléphone", "governorate": "Gouvernorat", "delegation": "Delegation", "establishment": "Etablissement", "schoolDegree": "niveau scolaire", "address": "<PERSON><PERSON><PERSON>", "cin": "CIN", "idImpersonal": "raison social/MF", "idConventional": "Num convention", "matricule": "Identifiant", "email": "Adresse électronique", "clientType": "Type de client", "password": "Mot de passe", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "errors": {"nameRequired": "Veuillez entrer la désignation", "dobRequired": "Veuillez entrer la date de naissance", "governorateRequired": "Veuillez entrer le gouvernorat", "delegationRequired": "Veuillez entrer la Délegation", "establishmentRequired": "Veuillez entrer l'établissement", "firstnameRequired": "Veuillez entrer le prénom", "lastnameRequired": "Veuillez entrer la désignation", "schoolDegreeRequired": "Veuillez entrer le niveau scolaire", "phoneRequired": "Veuillez entrer un numéro valide", "phoneInvalid": "Le numéro doit contenir 8 chiffres", "cinRequired": "Veuillez entrer le CIN", "cinInvalid": "L'identifiant doit contenir 8 chiffres", "addressRequired": "Veuillez entrer l'adresse", "emailRequired": "Veuillez entrer une adresse électronique valide", "clientTypeRequired": "Veuillez sélectionner le type d'abonnement", "noDegreeForAge": "Aucun niveau ne correspond à l'âge sélectionné. Veuillez vérifier votre date de naissance.", "faxRequired": "Veuillez entrer le fax", "faxInvalid": "Le fax doit contenir 8 chiffres", "withTVARequired": "Veuillez sélectionner si la société est soumise à la TVA", "legalRepresentativeRequired": "Veuillez entrer le représentant légal", "societyNameRequired": "Veuillez entrer le nom de la société"}, "filters": {"phone": "Filtrer par téléphone", "cin": "Filtrer par identifiant", "email": "Filtrer par email"}, "yes": "O<PERSON>", "no": "Non", "save": "Enregistrer", "error": "Erreur :"}, "manage_governorates": {"title": "Gouvernorats", "add": "Ajouter un gouvernorat", "edit": "Modifier le Gouvernorat", "details": "<PERSON>é<PERSON> du Gouvernorat", "save": "Enregistrer", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce gouvernorat ?", "noPO": "Aucun bon de commande disponible", "addPO": "Ajouter un bon de commande", "yes": "O<PERSON>", "no": "Non", "labels": {"id": "ID", "code": "Code", "name": "Désignation", "name_fr": "Désignation en Français", "name_en": "Désignation en Anglais", "name_ar": "Désignation en Arabe", "purchaseAmount": "Montant total", "hasPO": "<PERSON> de commande", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "placeholders": {"name_fr": "Entrez le Désignation en français", "name_en": "Entrez le Désignation en anglais", "name_ar": "Entrez le Désignation en arabe", "code": "Entrez le code"}, "filters": {"name_fr": "Filtrer par nom français", "name_en": "Filtrer par nom anglais", "name_ar": "Filtrer par nom arabe", "code": "Filtrer par code"}, "errors": {"nameFrRequired": "La désignation en français est requis", "nameEnRequired": "La désignation en anglais est requis", "nameArRequired": "La désignation en arabe est requis", "codeRequired": "Le code en arabe est requis"}, "po": {"title": "Gestion des bons de commande", "add": "Ajouter un bon de commande", "edit": "Modifier le bon de commande", "details": "<PERSON><PERSON><PERSON> du bon de commande", "save": "Enregistrer", "confirmAction": "Confirmation", "confirmAdd": "Êtes-vous sûr de vouloir ajouter ce bon de commande ?", "confirmUpdate": "Êtes-vous sûr de vouloir modifier ce bon de commande ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce bon de commande ?", "labels": {"reference": "Référence", "initialAmount": "Montant initial", "currentAmount": "Montant actuel", "status": "Statut", "date": "Date"}, "placeholders": {"reference": "Entrez la référence", "amount": "<PERSON><PERSON><PERSON> le montant", "status": "Sélectionnez le statut", "date": "Sélectionnez la date"}, "errors": {"referenceRequired": "La référence est requise", "amountRequired": "Le montant est requis", "dateRequired": "La date est requise"}}}, "manage_delegations": {"title": "Délégations", "add": "Ajouter une délégation", "edit": "Modifier la délégation", "details": "Détails de la délégation", "save": "Enregistrer", "confirmAction": "Confirmation", "confirmAdd": "Êtes-vous sûr de vouloir ajouter cette délégation ?", "confirmUpdate": "Êtes-vous sûr de vouloir modifier cette délégation ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette délégation ?", "yes": "O<PERSON>", "no": "Non", "labels": {"id": "ID", "name": "Désignation", "name_fr": "Désignation en Français", "name_en": "Désignation en Anglais", "name_ar": "Désignation en Arabe", "governorate": "Gouvernorat", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "placeholders": {"name_fr": "Entrez la désignation en français", "name_en": "Entrez la désignation en anglais", "name_ar": "Entrez la désignation en arabe", "governorate": "Sélectionnez le gouvernorat"}, "errors": {"nameFrRequired": "La désignation en français est requis", "nameEnRequired": "La désignation en anglais est requis", "nameArRequired": "La désignation en arabe est requis", "governorateRequired": "Le gouvernorat est requis"}, "filters": {"governorate": "Filtrer par gouvernorat"}}, "manage_schoolDegrees": {"title": "Niveaux scolaires", "add": "Ajouter un niveau scolaire", "edit": "Modifier le niveau scolaire", "details": "Détails du niveau scolaire", "confirmAction": "Confirmer l'Action", "confirmAdd": "Êtes-vous sûr de vouloir ajouter ce niveau scolaire ?", "confirmUpdate": "Êtes-vous sûr de vouloir modifier ce niveau scolaire ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce niveau scolaire ?", "labels": {"id": "ID", "name": "Désignation", "name_fr": "Désignation (Français)", "name_en": "Désignation (Anglais)", "name_ar": "Désignation (Arabe)", "establishmentType": "Type d'établissement", "ageMax": "Âge maximum", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "placeholders": {"name_fr": "Entrez la Désignation en français", "name_en": "Entrez la Désignation en anglais", "name_ar": "Entrez la Désignation en arabe", "ageMax": "Entrez l'âge maximum", "establishmentType": "Sélectionnez le type d'établissement"}, "filters": {"establishmentType": "Filtrer par type d'établissement"}, "errors": {"nameRequired": "La Désignation est requis", "ageMaxRequired": "L'âge maximum est requis", "establishmentTypeRequired": "Le type d'établissement est requis"}, "yes": "O<PERSON>", "no": "Non", "save": "Enregistrer", "error": "Erreur :"}, "manage_establishmentTypes": {"title": "Types d'établissements", "add": "Ajouter un type d'établissements", "edit": "Modifier le Type d'établissements", "details": "Voir les Détails type d'établissements", "confirmAction": "Confirmer l'Action", "confirmAdd": "Êtes-vous sûr de vouloir ajouter ce type d'établissement ?", "confirmUpdate": "Êtes-vous sûr de vouloir mettre à jour ce type d'établissement ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce type d'établissement ?", "labels": {"id": "ID", "name": "Désignation", "name_fr": "Désignation (Français)", "name_en": "Désignation (Anglais)", "name_ar": "Désignation (Arabe)", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "placeholders": {"id": "ID", "name_fr": "Entrez le Désignation en français", "name_en": "Entrez le Désignation en anglais", "name_ar": "Entrez le Désignation en arabe"}, "errors": {"idRequired": "L'ID est requis", "nameRequired": "la dest requis"}, "filters": {"name": "Filtrer par Désignation"}, "yes": "O<PERSON>", "no": "Non", "error": "Erreur :"}, "manage_assignAgents": {"title": "Affectation des cartes aux agents", "add": "Ajouter une affectation", "edit": "Modifier l'affectation", "details": "Détails de l'affectation", "confirmAction": "Confirmer l'action", "confirmAdd": "Êtes-vous sûr de vouloir ajouter cette affectation ?", "confirmUpdate": "Êtes-vous sûr de vouloir mettre à jour cette affectation ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette affectation ?", "yes": "O<PERSON>", "no": "Non", "save": "Enregistrer", "returned": "<PERSON><PERSON><PERSON><PERSON>", "cardStatus": {"title": "Remarque :", "description": "Les cartes peuvent être dans l'un de ces états :", "available": "Disponible (vert) :", "availableDesc": "Ajouté au stock et jamais affecté", "returned": "Retourné (jaune) :", "returnedDesc": "Précédemment affecté mais retourné au stock", "occupied": "Occupé (rouge) :", "occupiedDesc": "Actuellement affecté à un agent", "bothAvailable": "Les cartes disponibles et retournées peuvent être affectées aux agents."}, "messages": {"noAvailableSequences": "Aucune séquence disponible trouvée pour ce type de carte", "noOccupiedSequences": "Aucune séquence occupée trouvée pour ce type de carte", "noSequences": "Aucune séquence trouvée pour ce type de carte", "validatingSequence": "Validation de la séquence...", "sequenceAvailable": "La séquence est disponible", "sequenceNotAvailable": "La séquence n'est pas disponible", "loadingSequences": "Chargement des séquences...", "loadingStats": "Chargement des statistiques...", "errorLoadingSequences": "E<PERSON>ur lors du chargement des séquences", "recalculateSuccess": "Séquences recalculées avec succès", "recalculateError": "E<PERSON>ur lors du recalcul des séquences", "loading": "Chargement..."}, "labels": {"id": "ID", "salesPoint": "Point de vente", "agent": "Agent", "salesPeriod": "<PERSON><PERSON><PERSON><PERSON> de vente", "cardTypes": "Types de cartes", "cardTypesDetails": "Détails des types de cartes", "startNumber": "Numéro de d<PERSON>but", "endNumber": "Numéro de fin", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions", "availableSequences": "Séquences Disponibles", "occupiedSequences": "Séquences Occupées", "sequenceVisualization": "Visualisation des Séquences", "sequenceStats": "Statistiques des Séquences", "totalAvailable": "Total Disponible", "totalOccupied": "Total Occupé", "totalCards": "Total des Cartes", "totalUsed": "Total Utilisé", "cardsInRange": "cartes dans la plage"}, "placeholders": {"salesPoint": "Sélectionnez un point de vente", "agent": "Sélectionnez un agent", "salesPeriod": "Sélectionnez une période de vente", "cardTypes": "Sélectionnez des types de cartes", "startNumber": "Entrez le numéro de début", "endNumber": "Entrez le numéro de fin"}, "errors": {"salesPointRequired": "Le point de vente est requis", "agentRequired": "L'agent est requis", "salesPeriodRequired": "La période de vente est requise", "cardTypesRequired": "Les types de cartes sont requis", "startNumberRequired": "Le numéro de début est requis", "endNumberRequired": "Le numéro de fin est requis", "endNumberLessThanStart": "Le numéro de fin doit être supérieur ou égal au numéro de début", "sequenceNotAvailable": "Cette séquence n'est pas disponible", "sequenceValidationError": "Erreur lors de la validation de la séquence", "someSequencesNotAvailable": "Certaines séquences ne sont pas disponibles. Veuillez vérifier et réessayer."}, "minSerial": "Série min", "maxSerial": "Série max", "available": "Disponible", "occupied": "<PERSON><PERSON><PERSON><PERSON>", "selected": "Sélectionné", "availableRanges": "Plages disponibles", "noAvailableRanges": "Aucune plage disponible", "recalculate": "Recalculer"}, "manage_establishment": {"title": "Établissements", "add": "Ajouter un établissement", "edit": "Modifier un établissement", "details": "Détails de l'établissement", "selectGovernorate": "Sélectionnez un gouvernorat", "placeholders": {"name": "Désignation de l'établissement", "abbreviation": "Abréviation de l'établissement", "delegation": "Délégation de l'établissement", "establishmentType": "Type de l'établissement", "governorate": "Gouvernorat de l'établissement"}, "confirmAction": "Confirmer l'Action", "confirmAdd": "Êtes-vous sûr de vouloir ajouter cet établissement ?", "confirmUpdate": "Êtes-vous sûr de vouloir mettre à jour cet établissement ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cet établissement ?", "deleteSuccess": "Etablissement supprimé avec succès !", "labels": {"id": "Id", "name": "Désignation", "abbreviation": "Abréviation", "governorate": "Gouvernorat", "delegation": "Délégation", "establishmentType": "Type établissement", "createdAt": "<PERSON><PERSON><PERSON> le", "cnssEtab": "CNSS Etablissement", "actions": "Actions"}, "errors": {"nameRequired": "Veuillez entrer le Désignation", "delegationRequired": "Veuillez entrer la délégation", "governorateRequired": "Veuillez entrer le gouvernorat", "abbreviationRequired": "Veuillez entrer l'abréviation", "establishmentTypeRequired": "Veuillez entrer le type établissement", "cnssEtabRequired": "Veuillez entrer le CNSS Etablissement", "addressRequired": "Veuillez entrer l'adresse"}, "filters": {"name": "Filtrer par Désignation", "abbreviation": "Filtrer par abréviation", "delegation": "Filtrer par délégation", "governorate": "Filtrer par gouvernorat", "cnssEtab": "Filtrer par CNSS Etablissement", "establishmentType": "Filtrer par type établissement"}, "yes": "O<PERSON>", "no": "Non", "save": "Enregistrer", "error": "Erreur :"}, "manage_abnTypes": {"title": "Types d'abonnements", "add": "ajouter un type d'abonnement", "edit": "Modifier le type d'abonnement", "details": "Détails du type d'abonnement", "save": "Enregistrer", "confirmAction": "Confirmer l'Action", "confirmAdd": "Êtes-vous sûr de vouloir ajouter ce type ?", "confirmUpdate": "Êtes-vous sûr de vouloir mettre à jour ce type ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce type ?", "yes": "O<PERSON>", "no": "Non", "labels": {"id": "Id", "name": "Désignation", "hasCIN": "Possède CIN ?", "isStudent": "Suit des études ?", "isImpersonal": "Impersonnelle ?", "color": "<PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "errors": {"nameRequired": "La Désignation est requis", "colorRequired": "La couleur est requise"}, "placeholders": {"name": "Entrez le Désignation", "color": "Entrez la couleur"}, "filters": {"id": "Rechercher par l'identifiant", "name": "Rechercher par le Désignation"}}, "manage_paymentMethods": {"title": "Méthodes de paiments", "add": "Ajouter une méthode de Paiement", "edit": "Modifier la compagne", "details": "Dé<PERSON> de la méthode de Paiement", "confirmAction": "Confirmer l'Action", "confirmAdd": "Êtes-vous sûr de vouloir ajouter cette méthode de paiement ?", "confirmUpdate": "Êtes-vous sûr de vouloir modifier cette méthode de paiement ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette méthode de paiement ?", "labels": {"id": "ID", "name": "Désignation", "name_fr": "Désignation (Français)", "name_en": "Désignation (Anglais)", "name_ar": "Désignation (Arabe)", "status": "Statut", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "status": {"active": "Actif", "inactive": "Inactif"}, "placeholders": {"name_fr": "Entrez la Désignation en français", "name_en": "Entrez la Désignation en anglais", "name_ar": "Entrez la Désignation en arabe"}, "errors": {"nameRequired": "La Désignation est requis"}, "filters": {"name": "Rechercher par Désignation"}, "yes": "O<PERSON>", "no": "Non", "save": "Enregistrer", "error": "Erreur :"}, "manage_duplicateMotifs": {"title": "Motifs de duplication", "add": "Ajouter un motif de duplication", "edit": "Modifier le motif de duplication", "details": "Détails du motif de duplication", "save": "Enregistrer", "confirmAction": "Confirmation", "confirmAdd": "Voulez-vous vraiment ajouter ce motif de duplication ?", "confirmUpdate": "Voulez-vous vraiment mettre à jour ce motif de duplication ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce motif de duplication ?", "yes": "O<PERSON>", "no": "Non", "labels": {"id": "Id", "name": "Désignation", "name_fr": "Désignation en français", "name_en": "Désignation en anglais", "name_ar": "Désignation en arabe", "cardType": "Type de carte", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "errors": {"nameRequired": "la désignation est requise", "cardTypesRequired": "Le type de carte est requis"}, "placeholders": {"name": "Entrez la désignation", "name_fr": "Entrez le Désignation en français", "name_en": "Entrez le Désignation en anglais", "name_ar": "Entrez le Désignation en arabe", "cardType": "Sélectionnez le type de carte"}, "filters": {"name": "Rechercher par la désignation", "cardType": "Rechercher par type de carte"}}, "manage_configs": {"title": "Configurations système", "add": "Ajouter une configuration", "edit": "Modifier la configuration", "details": "Détails de la configuration", "save": "Enregistrer", "confirmAction": "Confirmation", "confirmAdd": "Voulez-vous vraiment ajouter cette configuration ?", "confirmUpdate": "Voulez-vous vraiment mettre à jour cette configuration ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette configuration ?", "yes": "O<PERSON>", "no": "Non", "types": {"string": "<PERSON><PERSON><PERSON>", "integer": "<PERSON><PERSON>", "boolean": "Booléen", "json": "JSON", "array": "<PERSON><PERSON>"}, "groups": {"system": "Système", "app": "Application", "email": "Email", "payment": "Paiement", "notification": "Notification"}, "labels": {"id": "Id", "key": "Clé", "value": "<PERSON><PERSON>", "type": "Type", "group": "Groupe", "label": "Libellé", "label_fr": "Libell<PERSON> (Français)", "label_en": "Libellé (Anglais)", "label_ar": "Libellé (Arabe)", "description_fr": "Description (Français)", "description_en": "Description (Anglais)", "description_ar": "Description (Arabe)", "is_public": "Public", "is_system": "Système", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "placeholders": {"key": "Entrez la clé de configuration", "value": "Entrez la valeur de configuration", "type": "Sélectionnez le type de configuration", "group": "Sélectionnez le groupe de configuration", "label_fr": "Entrez le libellé en français", "label_en": "Entrez le libellé en anglais", "label_ar": "Entrez le libellé en arabe", "description_fr": "Entrez la description en français", "description_en": "Entrez la description en anglais", "description_ar": "Entrez la description en arabe"}, "errors": {"keyRequired": "La clé est requise", "valueRequired": "La valeur est requise", "typeRequired": "Le type est requis", "groupRequired": "Le groupe est requis", "labelFrRequired": "Le libellé en français est requis", "labelEnRequired": "Le libellé en anglais est requis", "labelArRequired": "Le libellé en arabe est requis"}, "filters": {"key": "Filtrer par clé", "value": "Filtrer par valeur", "type": "Filtrer par type", "group": "Filtrer par groupe"}}, "manage_cardsFees": {"title": "Frais des cartes", "add": "Ajouter un frais", "edit": "Modifier le frais", "details": "<PERSON><PERSON><PERSON> du frais", "save": "Enregistrer", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce frais ?", "confirmAction": "Confirmation", "confirmAdd": "Voulez-vous vraiment ajouter ce frais ?", "confirmUpdate": "Voulez-vous vraiment mettre à jour ce frais ?", "yes": "O<PERSON>", "no": "Non", "labels": {"id": "Id", "name": "Désignation", "name_fr": "Désignation (Français)", "name_en": "Désignation (Anglais)", "name_ar": "Désignation (Arabe)", "abnType": "Type d'abonnement", "amount": "Montant en TND", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "errors": {"nameRequired": "La désignation est requise", "nameFrRequired": "La désignation en français est requise", "nameEnRequired": "La désignation en anglais est requise", "nameArRequired": "La désignation en arabe est requise", "abnTypeRequired": "Le type d'abonnement est requis", "amountRequired": "Le montant est requis"}, "placeholders": {"name": "Entrez la désignation", "name_fr": "Entrez la désignation en français", "name_en": "Entrez la désignation en anglais", "name_ar": "Entrez la désignation en arabe", "abnType": "Sélectionnez le type d'abonnement", "amount": "<PERSON><PERSON><PERSON> le montant"}, "filters": {"name": "Rechercher par le Désignation", "montant": "Rechercher par montant", "abnType": "Rechercher par type d'abonnement"}}, "manage_stations": {"title": "Stations", "labels": {"id": "Id", "name": "Désignation", "address": "<PERSON><PERSON><PERSON>", "governorate": "Gouvernorat", "delegation": "Délégation", "stationType": "Type", "latitude": "Latitude", "longitude": "Longitude", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "confirmDelete": "Êtes-vous sûr de vouloir supprimer cet arrêt ?", "yes": "O<PERSON>", "no": "Non", "add": "Ajouter une station", "edit": "Modifier la station", "details": "Détails de la station", "save": "Enregistrer", "filters": {"name": "Filtrer par Désignation", "governorate": "Filtrer par gouvernorat", "delegation": "Filtrer par délegation", "stationType": "Filtrer par type de station"}, "errors": {"nameRequired": "La Désignation est requis", "addressRequired": "L'adresse est requise", "delegationRequired": "La délégation est requise", "governorateRequired": "La gouvernorat est requise", "stationTypeRequired": "Le type de station est requis", "latitudeRequired": "La latitude est requise", "longitudeRequired": "La longitude est requise"}, "placeholders": {"name": "Entrez la Désignation de station", "address": "Entrez l'adresse de station", "governorate": "Sélectionnez un gouvernorat", "delegation": "Sélectionnez une délégation", "stationType": "Sélectionner le type de station", "latitude": "Saisir la latitude", "longitude": "Saisir la longitude"}, "stationTypes": {"intermediate": "Intermédiaire", "terminus": "Terminus", "hidden": "Caché"}, "view": "Voir la station", "confirmAction": "Confirmer l'action", "confirmUpdate": "Êtes-vous sûr de vouloir mettre à jour cette station ?", "confirmAdd": "Êtes-vous sûr de vouloir ajouter cette station ?", "selectGovernorate": "Veuillez d'abord sélectionner un gouvernorat", "validation": {"nameFrRequired": "Le nom en français est requis", "nameEnRequired": "Le nom en anglais est requis", "nameArRequired": "Le nom en arabe est requis", "governorateRequired": "Le gouvernorat est requis"}}, "manage_routes": {"title": "Trajets", "title_inter_station": "Gestion des trajets entre les stations", "add": "Ajouter un trajet d'abonnement", "edit": "Modifier le trajet", "details": "<PERSON><PERSON><PERSON> du trajet", "yes": "O<PERSON>", "no": "Non", "save": "Enregistrer", "options_regular": {"yes": "O<PERSON>", "no": "Non"}, "options": {"one_way": "Aller simple", "round_trip": "Aller-retour"}, "confirmAction": "Confirmer l'action", "confirmAdd": "Êtes-vous sûr de vouloir ajouter ce trajet ?", "confirmUpdate": "Êtes-vous sûr de vouloir mettre à jour ce trajet ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce trajet ?", "labels": {"id": "Id", "name": "Désignation", "status": "Status", "abnType": "Type d'abonnement", "numberOfKm": "kilométrage", "delegation": "Délégation", "line": "Ligne", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions", "manual_tariff": "<PERSON><PERSON><PERSON>", "station_depart": "<PERSON><PERSON><PERSON><PERSON>", "station_arrival": "Arrivée", "base_tariff": "base tarifaire", "regular": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "voyage_number": "N° de voyages"}, "filters": {"name": "Rechercher un trajet", "regular": "Sélectionner le type de tarif appliqué"}, "placeholders": {"name": "Entrez la Désignation du trajet", "status": "Sélectionnez le status", "delegation": "Sélectionnez une délégation", "abnType": "Sélectionnez le type d'abonnement", "governorate": "Sélectionnez un gouvernorat", "station_depart": "Sélectionnez la station de départ", "station_arrival": "Sélectionnez la station d'arrivée", "base_tariff": "Sélectionnez la base tarifaire", "manual_tariff": "En<PERSON><PERSON> le tarif manuel", "voyage_number": "Sélectionner le type de voyage", "numberOfKm": "Entrez le nombre des Kilomètres", "regular": "Sélectionner le type de tarif appliqué", "line": "Sélectionnez une ligne"}, "errors": {"nameRequired": "La Désignation du parcours est requis", "baseTariffRequired": "La base tarifaire est requise", "manualTariffRequired": "Le tarif manuel est requis", "governorateRequired": "Le gouvernorat est requis.", "delegationDepartRequired": "La délégation de départ est requise", "delegationArrivalRequired": "La délégation d'arrivée est requise", "stationDepartRequired": "La station de départ est requise", "stationArrivalRequired": "La station d'arrivée est requise", "voyageNumberRequired": "Le type de voyage est requis", "isRegularRequired": "Le type de tarif est requis", "numberOfKmRequired": "Le Kilomètrage est requis", "abnTypeRequired": "Le type d'abonnement est requis", "lineRequired": "La ligne est requise"}, "select_depart_arrival": "Sélectionner départ et arrivée"}, "manage_lines": {"title": "<PERSON><PERSON><PERSON>", "add": "Ajouter une ligne", "save": "Enregistrer", "addStation": "Ajouter des stations", "editStation": "Modifier les stations", "addDepartureTime": "Ajouter des heures de départ", "noSeasons": "Aucune saison disponible", "title_inter_station": "Gestion des trajets entre les stations", "confirmAction": "Confirmer l'action", "confirmAdd": "Êtes-vous sûr de vouloir ajouter cette ligne ?", "confirmUpdate": "Êtes-vous sûr de vouloir mettre à jour cette ligne ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette ligne ?", "labels": {"code_line": "Code ligne", "name": "Désignation", "status": "Status", "schedules": "<PERSON><PERSON><PERSON>", "numberOfKm": "Kilométrage", "commercial_speed": "Vitesse", "routes": "Trajets", "route": "<PERSON><PERSON><PERSON>", "departureTimes": "<PERSON><PERSON><PERSON><PERSON>", "serviceType": "Type de service", "comfort": "<PERSON><PERSON>", "normal": "Normal", "position": "Position", "createdAt": "Date de création", "actions": "Actions", "stations": "stations"}, "filters": {"code_line": "Rechercher par code de ligne", "name": "Rechercher par Désignation", "status": "Sélectionner un statut", "serviceType": "Sélectionner un type de service", "commercial_speed": "Rechercher par vitesse commerciale"}, "status": {"active": "Actif", "inactive": "Inactif"}, "actions": {"view": "Voir", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "placeholders": {"code_line": "Entrez le code de la ligne", "name": "Entrez le Désignation", "status": "Sélectionnez un statut", "commercial_speed": "Entrez la vitesse commerciale", "selectStation": "Sélectionnez une station", "position": "Entrez la position", "serviceType": "Sélectionnez un type de service", "selectStationType": "Sélectionnez un type de station"}, "messages": {"no_routes": "Aucun Station disponible", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette ligne ?", "yes": "O<PERSON>", "no": "Non", "no_route_assigned": "Aucun Station assignée", "unknownRoute": "Station inconnue", "loggedIn": "Connecté avec succès"}, "errors": {"codeLineRequired": "Le code de la ligne est requis", "nameRequired": "La Désignation est requis", "selectRoute": "Veuillez sélectionner un trajet", "positionRequired": "La position est requise", "selectDepartureTimeRequired": "Veuillez sélectionner une heure de départ", "serviceTypeRequired": "Veuillez sélectionner un type de service", "stationRequired": "Veuillez sélectionner une station", "manualTariffRequired": "Veuillez saisir le tarif", "departureTimeRequired": "les temps de départs sont requis"}, "addStationAssignment": "Affecter des stations", "stations": "Stations associées", "addstation": "Ajouter une Station", "direction": {"normal": "Direction normale", "reverse": "Inverser la direction"}}, "manage_rolePerm": {"title": "Gestion des rôles et permissions", "add": "Ajouter un rôle", "edit": "Modifier un rôle", "details": "<PERSON>é<PERSON> du rôle", "labels": {"name": "Désignation", "guard_name": "Désignation du garde", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "placeholders": {"name": "Désignation du rôle", "guard_name": "Désignation du garde"}, "deleteSuccess": "Rôle supprimé avec succès !", "errors": {"nameRequired": "Veuillez entrer la Désignation", "guard_nameRequired": "Veuillez entrer la Désignation du garde"}, "filters": {"name": "Filtrer par Désignation", "guard_name": "Filtrer par Désignation du garde"}, "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce rôle ?", "confirmAction": "Confirmation", "confirmAdd": "Voulez-vous vraiment ajouter ce rôle ?", "confirmUpdate": "Voulez-vous vraiment mettre à jour ce rôle ?", "yes": "O<PERSON>", "no": "Non", "save": "Enregistrer", "error": "Erreur :", "permissions": {"unassigned_permissions": "Permissions Non Attribuées", "assigned_permissions": "Permissions Attribuées"}, "roles": {"unassigned_roles": "Roles Non Attribuées", "assigned_roles": "Roles Attribuées"}}, "manage_campaignTypes": {"title": "Type compagnes", "add": "Ajouter un type compagne", "edit": "Modifier un type compagne", "details": "Détails de type compagne", "placeholders": {"name": "Désignation de type compagne"}, "confirmDelete": "Êtes-vous sûr de vouloir supprimer cet type compagne ?", "deleteSuccess": "Type compagne supprimé avec succès !", "labels": {"id": "Id", "name": "Désignation", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "errors": {"nameRequired": "Veuillez entrer la Désignation"}, "filters": {"name": "Filtrer par Désignation"}, "yes": "O<PERSON>", "no": "Non", "save": "Enregistrer", "error": "Erreur :"}, "manage_campaigns": {"title": "Compagnes", "add": "Ajouter une compagne", "edit": "Modifier la compagne", "details": "Détails de la compagne", "save": "Enregistrer", "salesPeriods": "Périodes de vente", "addSalesPeriod": "Ajouter une période de vente", "addFirstPeriod": "Ajouter la première période", "noSalesPeriods": "Aucune période de vente disponible", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette compagne ?", "confirmAction": "Confirmation", "confirmAdd": "Voulez-vous vraiment ajouter cette compagne ?", "confirmUpdate": "Voulez-vous vraiment mettre à jour cette compagne ?", "confirmDeletePeriod": "Êtes-vous sûr de vouloir supprimer cette période de vente ?", "period": {"edit": "Modifier la pèriode", "delete": "Supprimer la pèriode"}, "yes": "O<PERSON>", "no": "Non", "salesPeriodDeleted": "Période de vente supprimée avec succès", "salesPeriodSaved": "Période de vente enregistrée avec succès", "error": "Une erreur s'est produite", "labels": {"name": "Désignation", "campaignType": "Type compagne", "period": "Période", "status": "Statut", "salesPeriods": "Périodes de vente", "actions": "Actions", "startDate": "Date de début", "endDate": "Date de fin", "periodName": "Désignation de la période", "abnType": "Type d'abonnement"}, "filters": {"name": "Rechercher par Désignation", "campaignType": "Rechercher par type de compagne", "abnType": "Rechercher par type d'abonnement"}, "placeholders": {"name": "Entrez la Désignation de la compagne", "campaignType": "Entrez le type de compagne", "startDate": "Sélectionnez la date de début", "endDate": "Sélectionnez la date de fin", "periodName": "Entrez la Désignation de la période", "status": "Sélectionnez status", "selectAbnType": "Sélectionner le type d'abonnement"}, "errors": {"nameRequired": "La Désignation de la compagne est requis", "statusRequired": "Le statut de la compagne est requis", "startDateRequired": "La date de début est requise", "endDateRequired": "La date de fin est requise", "periodNameRequired": "La Désignation de la période est requis", "campaignTypeRequired": "Le type de compagne est requis", "abnTypeRequired": "Le type d'abonnement est requis"}, "open": "Ouverte", "closed": "Ferm<PERSON>", "WEEKLY": "<PERSON><PERSON><PERSON>", "MONTHLY": "<PERSON><PERSON><PERSON>", "YEARLY": "<PERSON><PERSON><PERSON>", "BIANNUAL": "<PERSON><PERSON><PERSON><PERSON>", "salesPeriodsCount": "Nombre de périodes de vente", "salesPeriodModal": "Gestion des périodes de vente"}, "manage_salesPeriods": {"title": "Périodes de ventes", "periodsAutoUpdated": "{{count}} p<PERSON><PERSON>de(s) de vente automatiquement fermée(s) car la date de fin est dépassée", "labels": {"id": "Id", "name": "Désignation", "name_fr": "Désignation en français", "name_en": "Désignation en anglais", "name_ar": "Désignation en arabe", "campaignType": "Type compagne", "campaign": "<PERSON><PERSON>ag<PERSON>", "period": "Période", "abnType": "Type abonnements", "startDate": "Date de début", "endDate": "Date de fin", "status": "Status", "actions": "Actions"}, "filters": {"name": "Rechercher par Désignation", "campaignType": "Rechercher par type compagne", "abnType": "Rechercher par type d'abonnements", "status": "Sélectionnez le status"}, "noCampaign": "Aucune compagne", "open": "Ouvert", "closed": "<PERSON><PERSON><PERSON>", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette périodes de vente ?", "confirmAction": "Confirmation", "confirmAdd": "Voulez-vous vraiment ajouter cette périodes de vente ?", "confirmUpdate": "Voulez-vous vraiment mettre à jour cette périodes de vente ?", "yes": "O<PERSON>", "no": "Non", "add": "Ajouter une période de vente", "edit": "Modifier la période de vente", "details": "Dé<PERSON> de la période de vente", "save": "Enregistrer", "errors": {"nameRequired": "La Désignation est requis.", "statusRequired": "Le statut est requis.", "nameFrRequired": "Le nom en français est requis", "nameEnRequired": "Le nom en anglais est requis", "nameArRequired": "Le nom en arabe est requis", "startDateRequired": "La date de début est requise", "endDateRequired": "La date de fin est requise", "campaignRequired": "La compagne est requise", "abnTypeRequired": "Le type d'abonnement est requis"}, "placeholders": {"name": "Entrez la Désignation", "name_fr": "Entrez le nom en français", "name_en": "Entrez le nom en anglais", "name_ar": "Entrez le nom en arabe", "status": "Sélectionnez le status", "startDate": "Sélectionnez la date de début", "endDate": "Sélectionnez la date de fin", "campaign": "Sélectionnez une compagne", "abnType": "Sélectionnez un type d'abonnement", "selectCampaign": "Sélectionnez une compagne"}}, "manage_civilSalesPeriods": {"labels": {"id": "Id", "name": "Désignation", "schoolCampaign": "Compagne scolaire", "period": "Période", "isOpen": "Statut", "actions": "Actions", "startDate": "Date de début", "endDate": "Date de fin"}, "filters": {"name": "Filtrer par Désignation"}, "placeholders": {"name": "Saisir la Désignation...", "selectCampaign": "Sélectionner une compagne...", "startDate": "Date de début...", "endDate": "Date de fin..."}, "errors": {"nameRequired": "La Désignation est requis", "isOpenRequired": "Le statut est requis", "schoolCampaignRequired": "La compagne scolaire est requise", "startDateRequired": "La date de début est requise", "endDateRequired": "La date de fin est requise"}, "title": "<PERSON><PERSON><PERSON> les périodes de vente civile", "add": "Ajouter une période de vente civile", "save": "Enregistrer", "yes": "O<PERSON>", "no": "Non", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette période ?", "open": "Ouvert", "closed": "<PERSON><PERSON><PERSON>", "noCampaign": "Aucune compagne associée", "details": "Dé<PERSON> de la période", "edit": "Modifier la période", "deleteSuccess": "Période de vente civile supprimée avec succès !"}, "manage_salesPoints": {"title": "Points de ventes", "add": "Ajouter un point de vente", "edit": "Modifier le point de vente", "details": "Dé<PERSON> du point de vente", "save": "Enregistrer", "yes": "O<PERSON>", "no": "Non", "assignedCards": "Cartes assignées", "selectPeriod": "Sélectionnez une période", "selectPeriodFirst": "Veuillez sélectionner une période de vente", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce point de vente ?", "confirmDeleteAgent": "Êtes-vous sûr de vouloir supprimer cet agent ?", "confirmAction": "Confirmer l'action", "confirmAdd": "Êtes-vous sûr de vouloir ajouter ce point de vente ?", "confirmUpdate": "Êtes-vous sûr de vouloir modifier ce point de vente ?", "noAgents": "Aucun agent", "addAgent": "Ajouter un agent", "editAgent": "Modifier un agent", "detailsAgent": "<PERSON><PERSON><PERSON> d'un agent", "labels": {"id": "ID", "name": "Désignation", "name_fr": "Désignation (Français)", "name_en": "Désignation (Anglais)", "name_ar": "Désignation (Arabe)", "agents": "Gestion des agents", "contact": "Contact", "address": "<PERSON><PERSON><PERSON>", "agency": "Agence", "status": "Status", "governorate": "Gouvernorat", "delegation": "Délégation", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "placeholders": {"name_fr": "<PERSON><PERSON> le nom en français", "name_en": "<PERSON><PERSON> le nom en anglais", "name_ar": "<PERSON><PERSON> le nom en arabe", "contact": "Saisir les informations de contact", "address": "<PERSON><PERSON> l'adresse", "agency": "Sé<PERSON><PERSON>ner une agence", "status": "Sélectionner le statut", "governorate": "Sélectionner un gouvernorat", "delegation": "Sélectionner une délégation"}, "errors": {"nameFrRequired": "Le nom en français est obligatoire", "nameEnRequired": "Le nom en anglais est obligatoire", "nameArRequired": "Le nom en arabe est obligatoire", "contactRequired": "Le contact est obligatoire", "addressRequired": "L'adresse est obligatoire", "agencyRequired": "L'agence est obligatoire", "governorateRequired": "Le gouvernorat est obligatoire", "delegationRequired": "La délégation est obligatoire", "contactInvalid": "Le contact doit être composé de 8 chiffres"}, "selectGovernorate": "Veuillez d'abord sélectionner un gouvernorat"}, "manage_periodicities": {"title": "Périodicitées", "add": "Ajouter une pèriodicité", "edit": "Modifier une pèriodicité", "details": "Détails d'une pèriodicité", "save": "Enregistrer", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette pèriodicité ?", "confirmAction": "Confirmer l'action", "confirmAdd": "Êtes-vous sûr de vouloir ajouter cette pèriodicité ?", "confirmUpdate": "Êtes-vous sûr de vouloir modifier cette pèriodicité ?", "yes": "O<PERSON>", "no": "Non", "labels": {"id": "Id", "name": "Désignation", "periodicityType": "Code", "maxReposDaysNumber": "<PERSON> jour /semaine", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "errors": {"nameRequired": "La Désignation du période d'abonnement est requis", "periodicityType": "La pèriodicité est requis", "maxReposDaysNumberRequired": "Le Nombre de jours de repos maximal est requis"}, "placeholders": {"name": "Saisissez la Désignation de la periode d'abonnement", "daysNumber": "Saisissez le Nombre de jours", "periodicityType": "Sélectionnez le type de pèriodicité", "maxReposDaysNumber": "Saisissez max jour /semaine"}, "filters": {"name": "Rechercher par Désignation", "periodicityType": "Rechercher par pèriodicité", "maxRestDays": "Rechercher par nombre de jours de repos maximal"}}, "manage_clientTypes": {"title": "Types clients", "add": "Ajouter un type de client", "edit": "Modifier un type de client", "details": "Détails d'un type de client", "save": "Enregistrer", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce type de client ?", "confirmAction": "Confirmer l'action", "confirmAdd": "Êtes-vous sûr de vouloir ajouter ce type de client ?", "confirmUpdate": "Êtes-vous sûr de vouloir modifier ce type de client ?", "yes": "O<PERSON>", "no": "Non", "labels": {"id": "Id", "name": "Désignation", "abnType": "Type abonnement", "hasCIN": "Possède CIN ?", "isStudent": "Suit des études ?", "isImpersonal": "Impersonnelle ?", "color": "<PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "errors": {"nameRequired": "La Désignation type de client est requis", "hasCINRequired": "Veuillez choisir si ce type possède CIN", "abnTypeRequired": "Veuillez choisir le Type d'abonnement", "colorRequired": "La couleur est requise", "isStudentRequired": "Veuillez choisir si ce type suit des études"}, "placeholders": {"name": "Saisissez la Désignation de type de client", "abnType": "Saisissez le type abonnement", "color": "Entrez la couleur"}, "filters": {"name": "Rechercher par Désignation", "hasCIN": "Possède CIN ?", "abnType": "Rechercher par type abonnement", "isStudent": "Suit des études ?"}}, "manage_newSubs": {"title": "Abonnements", "add": "Ajouter un abonnement", "edit": "Modifier l'abonnement", "details": "Détails de l'abonnement", "renewal": "Renouvellement d'abonnement", "selectSubscriptionType": "Sélectionnez le type d'abonnement", "save": "Enregistrer", "createSuccess": "Abonnement créé avec succès !", "updateSuccess": "Abonnement mis à jour avec succès !", "renewalSuccess": "Abonnement renouvelé avec succès !", "deleteSuccess": "Abonnement supprimé avec succès !", "upload": "Télécharger une photo", "cropImage": "Recadrer la photo", "noPhoto": "Aucune photo", "backgroundRequirement": "L'image doit avoir un fond blanc", "selectSubscriptionPrompt": "Veuillez sélectionner un type d'abonnement", "selectDepartureFirst": "Veuillez d'abord sélectionner une station de départ", "confirmPayment": "Confirmer le paiement", "confirmCrop": "Enregistrer", "confirmAction": "Confirmer l'action", "confirmAdd": "Êtes-vous sûr de vouloir ajouter cet abonnement ?", "confirmUpdate": "Êtes-vous sûr de vouloir modifier cet abonnement ?", "confirmRenewal": "Êtes-vous sûr de vouloir renouveler cet abonnement ?", "confirmPrint": "Êtes-vous sûr de vouloir imprimer cette carte d'abonnement ?", "addClient": "Nouvel abonné", "alreadyPaid": "<PERSON><PERSON><PERSON><PERSON> pay<PERSON>", "notPaid": "Non payé", "canceled": "<PERSON><PERSON><PERSON>", "pending": "En attente", "cancel": "Annuler", "days": "Jours", "clientInfo": "Informations sur l'abonné", "subscriptionInfo": "Informations sur l'abonnement", "modal": {"cardTitle": "Impression de la carte d'abonnement", "cardDuplicate": "Duplication de la carte d'abonnement", "motifDuplicate": "<PERSON><PERSON><PERSON> de duplication", "new_amount": "Nouvelle montant d'abonnement", "print": "<PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>"}, "select": {"keep_old_amount": "Garder Ancien Montant", "free_edit": "Saisie libre"}, "card": {"title": "Impression de la carte d'abonnement", "name": "Nom", "number": "Numéro de carte", "expiry": "Date d'expiration", "footerText": "Merci d'être un membre précieux !"}, "restDays": {"1": "<PERSON><PERSON>", "2": "<PERSON><PERSON>", "3": "<PERSON><PERSON><PERSON><PERSON>", "4": "<PERSON><PERSON>", "5": "<PERSON><PERSON><PERSON><PERSON>", "6": "<PERSON><PERSON>", "7": "<PERSON><PERSON><PERSON>"}, "paymentOptions": {"payed": "<PERSON><PERSON>", "notPayed": "Non payé", "canceled": "<PERSON><PERSON><PERSON>"}, "options": {"withVacation": "Avec vacances", "withoutVacation": "Sans vacances"}, "tooltips": {"payment": "Détails et paiement", "renew": "Renouveler l'abonnement", "duplicat": "Dupliquer l'abonnement", "viewCard": "Voir la carte d'abonnement"}, "filters": {"client": "Rechercher par identifiant (abonné)"}, "customDiscountApplied": "Remise personnalisée appliquée", "labels": {"id": "Id", "default": "<PERSON><PERSON> <PERSON><PERSON>", "restDays": "Nombre de jours de repos", "phone": "Téléphone", "clientName": "Nom & Prénom", "dob": "Date de naissance", "clientType": "Type de client", "clientPhysique": "Client Physique", "clientMoral": "Client <PERSON>", "withTVA": "Avec TVA", "stagiaire": "Stagiaire", "socialAffair": "Affaire sociale", "isStagiaire": "Stagiaire", "stageStartDate": "Date de début de stage", "stageEndDate": "Date de fin de stage", "socialAffairYes": "Affaire sociale", "socialAffairNo": "Ré<PERSON>lier", "specialClient": "Abonné spé<PERSON>", "isImpersonalMoral": "Impersonnel moral", "isWithTVA": "Avec TVA", "civil": "Civil", "universitaire": "Universitaire", "scolaire": "Scolaire", "periodicity": "Périodicité", "subsNumber": "Nombre des abonnés", "governorate_depart": "Gouvernorat <PERSON>", "governorate_arrival": "Gouvernorat d'arrivée", "delegation_depart": "Délégation de départ", "station_depart": "Station de départ", "delegation_arrival": "Délégation d'arrivée", "station_arrival": "Station d'arrivée", "image": "Image", "createdAt": "<PERSON><PERSON><PERSON> le", "clientCin": "Abonné par cin", "client": "<PERSON><PERSON><PERSON><PERSON>", "clientUniversitaire": "Abonné universitaire", "clientCivil": "Abonné civil", "clientImpersonal": "<PERSON><PERSON><PERSON><PERSON> impersonnel", "clientConventional": "Abonné conventionnel", "identityNumber": "N° Identifiant", "conventionNumber": "N° Convention", "cin": "CIN", "parentCin": "CIN parent", "establishment": "Etablissement", "schoolDegree": "Niveau scolaire", "matricule": "<PERSON><PERSON>", "socialAffairId": "Identifiant Affaires Sociales", "matriculate": "<PERSON><PERSON>", "clientDob": "Date de naissance", "line": "Ligne", "vacation": "Vacances", "withVacation": "Avec vacances", "withoutVacation": "Sans vacances", "totalAmount": "Montant total", "status": "Statut", "reversed": "Inversé", "reversedYes": "Inversé", "reversedNo": "Normal", "abnType": "Abonnement", "trip": "<PERSON><PERSON><PERSON>", "actions": "Actions", "paymentMethod": "Méthode de paiement", "paymentMethodPlaceholder": "Sélectionner une méthode de paiement", "customDiscount": "Remise personnalisée", "customDiscountPercentage": "Pourcentage de remise (%)"}, "errors": {"new_amount": "La nouvelle montant est requise", "motifDuplicate": "La motif de duplication est requise", "clientRequired": "Le nombre des abonnés est requis", "matriculateRequired": "L'id élève est requise", "clientDobRequired": "La date de naissance est requise", "subsNumberRequired": "L'abonné est requis", "restDaysRequired": "Le nombre de jours de repos est requis", "tooManyRestDays": "Vous ne pouvez pas sélectionner plus de {{max}} jours de repos pour la périodicité {{periodicity}}", "photoRequired": "La photo est requise", "periodicityRequired": "La périodicité est requise", "lineRequired": "La ligne est requise", "stageStartDateRequired": "La date de début de stage est requise", "stageEndDateRequired": "La date de fin de stage est requise", "vacationRequired": "Veuillez indiquer si l'abonnement inclut les vacances.", "governorateRequired": "Le gouvernorat est requis.", "delegationDepartRequired": "La délégation de départ est requise", "delegationArrivalRequired": "La délégation d'arrivée est requise", "stationDepartRequired": "La station de départ est requise", "stationArrivalRequired": "La station d'arrivée est requise", "specialClientRequired": "Le type d'abonné est requis", "socialAffairRequired": "L'Affaire sociale est requise", "statusRequired": "Le statut est requis", "abnTypeRequired": "Le type d'abonnement est requis", "unexpected": "Une erreur inattendue s'est produite", "cinRequired": "Le cin est requis", "idEleveRequired": "L'id élève est requis", "dobRequired": "La date de naissance est requise", "socialAffairIdRequired": "L'identifiant des affaires sociales est requis", "selectPaymentMethod": "Veuillez sélectionner une méthode de paiement"}, "messages": {"socialAffairIdInvalid": "Vous n’êtes pas éligible à une aide sociale.", "socialAffairIdValid": "Vous êtes éligible à une aide sociale."}, "placeholders": {"selectClientCin": "Sélectionner un abonné par cin", "selectClient": "Sélectionner un abonné", "matriculate": "Entrer id élève", "clientDob": "Entrer date de naissance", "subsNumber": "Entrer le nombre des abonnés", "restDays": "Sélectionner le nombre de jour de repos", "periodicity": "Sélectionner une périodicité", "abnType": "Sélectionnez le type d'abonnement", "status": "Sélectionnez le statut", "parentCin": "Entrer cin parent", "line": "Sé<PERSON><PERSON>ner une ligne", "stageStartDate": "Sélectionnez la date de début de stage", "stageEndDate": "Sélectionnez la date de fin de stage", "delegation": "Sélectionnez une délégation", "governorate": "Sélectionnez un gouvernorat", "station_depart": "Sélectionnez la station de départ", "station_arrival": "Sélectionnez la station d'arrivée", "socialAffair": "Sélectionnez l'affaire sociale", "specialClient": "Sélectionnez le type d'abonné", "socialAffairId": "Entrez l'identifiant des affaires sociales", "idEleve": "Entrer id élève", "cin": "Entrer cin", "new_amount": "Entrer le nouveau montant", "selectPaymentMethod": "Sélectionnez une méthode de paiement"}, "confirmDelete": "Êtes-vous sûr de vouloir supprimer cet abonnement ?", "yes": "O<PERSON>", "no": "Non", "close": "<PERSON><PERSON><PERSON>", "selectStationsFirst": "Veuillez d'abord sélectionner les stations de départ et d'arrivée"}, "manage_discounts": {"title": "Remises", "add": "Ajouter une remise", "edit": "Modifier la remise", "details": "<PERSON>é<PERSON> de la remise", "save": "Enregistrer", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette remise ?", "confirmAction": "Confirmation", "confirmAdd": "Voulez-vous vraiment ajouter cette remise ?", "confirmUpdate": "Voulez-vous vraiment mettre à jour cette remise ?", "yes": "O<PERSON>", "no": "Non", "labels": {"id": "ID", "name": "Désignation", "name_fr": "Désignation (Français)", "name_en": "Désignation (Anglais)", "name_ar": "Désignation (Arabe)", "subsType": "Type abonnement", "abnPeriod": "Périodes d'abonnement", "specialClient": "Abonné spé<PERSON>", "date_start": "Date de début", "date_end": "Date de fin", "discountPercentage": "Remise (%)", "is_stagiaire": "Stagiaire", "is_affaire_sociale": "Affaire sociale", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "errors": {"nameRequired": "Le nom est requis", "nameFrRequired": "Le nom en français est requis", "nameEnRequired": "Le nom en anglais est requis", "nameArRequired": "Le nom en arabe est requis", "discountPercentageRequired": "Le pourcentage de remise est requis", "subsTypeRequired": "Le type d'abonnement est requis", "abnPeriodRequired": "La période d'abonnement est requise", "dateStartRequired": "La date de début est requise", "dateEndRequired": "La date de fin est requise"}, "placeholders": {"name": "Saisissez le nom de la remise", "name_fr": "Saisissez le nom en français", "name_en": "Saisissez le nom en anglais", "name_ar": "Saisissez le nom en arabe", "discountPercentage": "Saisissez le pourcentage de remise", "subsType": "Sélectionnez le type d'abonnement", "abnPeriod": "Sélectionnez les périodes d'abonnement", "date_start": "Sélectionnez la date de début", "date_end": "Sélectionnez la date de fin"}, "filters": {"discountPercentage": "Rechercher par pourcentage", "subsType": "Rechercher par type d'abonnement", "abnPeriod": "Rechercher par période d'abonnement", "specialClient": "Rechercher par abonné spécial"}}, "manage_stockCards": {"title": "Stock cartes", "add": "Ajouter un stock", "edit": "Modifier un un stock", "details": "Détails de un stock", "ajout": "<PERSON><PERSON><PERSON>", "retour": "Retour", "placeholders": {"name": "Désignation du stock", "quantity": "Quantité du stock", "typeStock": "Type des cartes", "mouvement": "Mouvement", "status": "Status du stock", "initialQuantity": "Quantité initial", "remainingQuantity": "Quantité restante", "num_seq_start": "Numéro séquenciel de d<PERSON>", "num_seq_end": "Numéro séquenciel de fin", "cardTypes": "Sélectionnez le type de carte"}, "yes": "O<PERSON>", "no": "Non", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette carte ?", "labels": {"id": "Id", "name": "Désignation", "typeStock": "Type des cartes", "initialQuantity": "Quantité initial", "remainingQuantity": "Quantité restante", "quantity": "Quantité du stock", "status": "Status", "createdAt": "<PERSON><PERSON><PERSON> le", "mouvement": "Mouvement", "id_agent": "Agent", "id_card_type": "Type carte", "num_seq_start": "Début <PERSON>", "num_seq_end": "<PERSON>", "actions": "Actions"}, "errors": {"sequence_not_in_affectation": "La séquence de cartes doit faire partie d'une séquence d'affectation", "nameRequired": "La Désignation de stock est requis", "initialQuantityRequired": "Veuillez entrer la Quantité initial", "remainingQuantityRequired": "Veuillez entrer la Quantité restante", "statusRequired": "Veuillez sélectinner le status", "typeStockRequired": "Veuillez sélectionner le type des cartes", "quantityRequired": "Veuillez entrer la Quantité du stock", "num_seq_startRequired": "Numéro séquenciel de début est requis", "num_seq_endRequired": "Numéro séquenciel de fin est requis", "id_agentRequired": "Agent est requis", "id_card_typeRequired": "Type carte est requis", "sequence_out_of_range": "La séquence doit être comprise entre {min} et {max}", "sequence_end_before_start": "La fin de séquence doit être supérieure ou égale au début"}, "filters": {"name": "Saisissez la Désignation de stock", "initialQuantity": "Filtrer par Quantité initial", "remainingQuantity": "Filtrer par Quantité restante", "status": "Filtrer par status", "id_card_type": "Filtrer par type carte", "id_agent": "Filtrer par agent"}, "in_stock": "En stock", "out_of_stock": "Rupture", "save": "Enregistrer", "error": "Erreur :", "NORMAL": "normal", "DUPLICATA": "duplicata", "ANNUELLE": "annuelle", "PERIODIQUE": "pèriodique"}, "manage_agencies": {"title": "Agences", "add": "Ajouter une agence", "edit": "Modifier une agence", "salesPeriodModal": "<PERSON><PERSON><PERSON> les points de vente", "details": "Détails d'une une agence", "selectGovernorate": "Sélectionnez un gouvernorat", "salesPoints": "Points de vente", "addSalesPoint": "Ajouter un point de vente", "addFirstSalesPoint": "Ajouter le premier point de vente", "noSalesPoints": "Aucun point de vente disponible", "placeholders": {"governorate": "Sélectionnez un gouvernorat", "delegation": "Sélectionnez une délégation", "name": "Entrez la désignation", "name_fr": "Entrez la désignation en français", "name_en": "Entrez la désignation en anglais", "name_ar": "Entrez la désignation en arabe", "abnType": "Sélectionnez le type d'abonnement", "code": "Entrez le code", "contact": "Entrez le contact", "address": "Entrez l'adresse", "status": "Sélectionnez le status"}, "labels": {"id": "Id", "name": "Désignation", "name_fr": "Désignation en français", "name_en": "Désignation en anglais", "name_ar": "Désignation en arabe", "governorate": "Gouvernorat", "delegation": "Délégation", "code": "Code", "abnType": "Type d'abonnement", "contact": "Contact", "status": "Status", "address": "<PERSON><PERSON><PERSON>", "actions": "Actions", "createdAt": "<PERSON><PERSON><PERSON> le"}, "errors": {"name": "La désignation est requise", "nameFrRequired": "La désignation en français est requise", "nameEnRequired": "La désignation en anglais est requise", "nameArRequired": "La désignation en arabe est requise", "codeRequired": "Le code est requis", "contactRequired": "Le contact est requis", "addressRequired": "L'adresse est requise", "abnTypeRequired": "Le type d'abonnement est requis", "governorateRequired": "Le gouvernorat est requis", "statusRequired": "Le status est requis", "delegationRequired": "La délégation est requise", "contactInvalid": "Le contact doit être composé de 8 chiffres"}, "filters": {"name": "Rechercher par désignation", "name_fr": "Filtrer par désignation en français", "name_en": "Filtrer par désignation en anglais", "name_ar": "Filtrer par désignation en arabe", "code": "Filtrer par code", "contact": "Filtrer par contact", "address": "Filtrer par adresse", "governorate": "Filtrer par gouvernorat", "abnType": "Filtrer par type d'abonnement", "delegation": "Filtrer par délégation", "status": "Filtrer par status"}, "confirmAction": "Confirmer l'action", "confirmAdd": "Êtes-vous sûr de vouloir ajouter cette agence ?", "confirmUpdate": "Êtes-vous sûr de vouloir mettre à jour cette agence ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette agence ?", "yes": "O<PERSON>", "no": "Non", "save": "Enregistrer", "error": "Erreur :"}, "manage_cardTypes": {"title": "Types cartes", "add": "Ajouter un type de carte", "edit": "Modifier le type de carte", "details": "Détails du type de carte", "confirmAction": "Confirmer l'action", "confirmAdd": "Êtes-vous sûr de vouloir ajouter ce type de carte ?", "confirmUpdate": "Êtes-vous sûr de vouloir mettre à jour ce type de carte ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette agence ?", "deleteSuccess": "agence supprimé avec succès !", "labels": {"id": "Id", "code": "Code", "name": "Désignation", "nameFr": "Désignation en (français)", "nameEn": "Désignation en (anglais)", "nameAr": "Désignation en (arabe)", "address": "<PERSON><PERSON><PERSON>", "contact": "Contact", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "placeholders": {"name": "Entrez la Désignation", "nameFr": "Entrez la Désignation en français", "nameEn": "Entrez la Désignation en anglais", "nameAr": "Entrez la Désignation en arabe", "code": "Entrez le code", "address": "Entrez l'adresse", "contact": "Entrez le contact"}, "errors": {"nameRequired": "Veuillez entrer la Désignation", "nameFrRequired": "Veuillez entrer la Désignation en français", "nameEnRequired": "Veuillez entrer la Désignation en anglais", "nameArRequired": "Veuillez entrer la Désignation en arabe", "codeRequired": "Veuillez entrer le code", "addressRequired": "Veuillez entrer l'adresse", "contactRequired": "Veuillez entrer le contact"}, "filters": {"code": "Filtrer par code", "name": "Filtrer par Désignation", "nameFr": "Filtrer par Désignation en français", "nameEn": "Filtrer par Désignation en anglais", "nameAr": "Filtrer par Désignation en arabe", "address": "Filtrer par adresse", "contact": "Filtrer par contact"}, "yes": "O<PERSON>", "no": "Non", "save": "Enregistrer", "error": "Erreur :"}, "manage_seasons": {"title": "Saisons", "add": "Ajouter une saison", "edit": "Modifier la saison", "details": "Détails de la saison", "save": "Enregistrer", "confirmAction": "Confirmer l'action", "confirmAdd": "Êtes-vous sûr de vouloir ajouter cette saison ?", "confirmUpdate": "Êtes-vous sûr de vouloir mettre à jour cette saison ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette saison ?", "yes": "O<PERSON>", "no": "Non", "labels": {"id": "ID", "name": "Désignation", "name_fr": "Désignation en Français", "name_en": "Désignation en Anglais", "name_ar": "Désignation en Arabe", "period": "Période", "priority": "Priorité", "start_date": "Date de début", "end_date": "Date de fin", "actions": "Actions", "createdAt": "<PERSON><PERSON><PERSON> le"}, "placeholders": {"name_fr": "Entrez la désignation en français", "name_en": "Entrez la désignation en anglais", "name_ar": "Entrez la désignation en arabe", "start_date": "Sélectionnez la date de début", "end_date": "Sélectionnez la date de fin", "priority": "Entrez la priorité"}, "errors": {"name_required": "La désignation est requise", "start_date_required": "La date de début est requise", "end_date_required": "La date de fin est requise", "priority_required": "La priorité est requise"}}, "common": {"all": "Tous", "yes": "O<PERSON>", "km": "km", "no": "Non", "active": "Active", "rupture": "<PERSON><PERSON><PERSON><PERSON>", "inactive": "Inactive", "close": "<PERSON><PERSON><PERSON>", "open": "Ouvert", "closed": "<PERSON><PERSON><PERSON>", "save": "Enregistrer", "cancel": "Annuler", "edit": "Modifier", "search": "Vérifier", "export": "Exporter", "view": "Voir", "delete": "<PERSON><PERSON><PERSON><PERSON>", "loading": "Chargement...", "noData": "<PERSON><PERSON><PERSON> donnée disponible", "verifying": "Vérification en cours...", "verify": "Vérifier", "apply": "Appliquer", "na": "N/A", "tnd": "TND", "impression": "Impression", "reimpression": "Réimpression", "duplicata": "Du<PERSON><PERSON><PERSON>", "motif": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON>", "currentCard": "<PERSON>te actuelle", "previousCard": "Carte précédente", "cardNumber": "Numéro de carte", "cardDate": "Date d'émission", "noCardInfo": "Aucune information de carte disponible", "schedules": "<PERSON><PERSON><PERSON>"}, "manage_locationSeasons": {"title": "Saisons de location", "add": "Ajouter une saison de location", "edit": "Modifier la saison de location", "details": "Détails de la saison de location", "confirmAction": "Confirmation", "confirmAdd": "Voulez-vous vraiment ajouter cette saison de location ?", "confirmUpdate": "Voulez-vous vraiment mettre à jour cette saison de location ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette saison de location ?", "yes": "O<PERSON>", "no": "Non", "sections": {"basicInfo": "Informations de Base", "dateRange": "Période"}, "labels": {"id": "ID", "name": "Nom", "name_fr": "Nom en Français", "name_en": "Nom en Anglais", "name_ar": "Nom en Arabe", "startDate": "Date de Début", "endDate": "Date de Fin", "status": "Statut", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "Actions"}, "placeholders": {"name_fr": "Entrez le nom en français", "name_en": "Entrez le nom en anglais", "name_ar": "Entrez le nom en arabe", "startDate": "Sélectionnez la date de début", "endDate": "Sélectionnez la date de fin"}, "errors": {"nameFrRequired": "Le nom en français est requise", "nameEnRequired": "La désignation en anglais est requise", "nameArRequired": "La désignation en arabe est requise", "startDateRequired": "La date de début est requise", "endDateRequired": "La date de fin est requise", "endDateAfterStartDate": "La date de fin doit être postérieure à la date de début", "seasonOverlap": "Cette saison chevauche une saison existante"}}, "manage_typeVehiculeSaisonLocations": {"title": "Tarification véhicules", "add": "Ajouter un tarif", "bulkAdd": "Ajouter des tarifs en Masse", "edit": "Modifier le tarif", "details": "<PERSON><PERSON><PERSON> du tarif", "confirmAction": "Confirmation", "confirmAdd": "Voulez-vous vraiment ajouter ce tarif ?", "confirmUpdate": "Voulez-vous vraiment mettre à jour ce tarif ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce tarif ?", "confirmBulkAction": "Confirmation d'Action en Masse", "confirmBulkAdd": "Voulez-vous vraiment ajouter {totalEntries} tarifs pour {vehicleCount} types de véhicules et {seasonCount} saisons ?", "yes": "O<PERSON>", "no": "Non", "priceMatrix": "<PERSON><PERSON>", "selectVehicleAndSeason": "Veuillez sélectionner des types de véhicules et des saisons pour afficher la matrice de prix", "selectForComparison": "Sélectionner pour comparaison", "showComparison": "Afficher la Comparaison", "compare": "Comparer les <PERSON>", "priceComparison": "Comparaison de Prix", "noChanges": "Aucun changement à enregistrer", "labels": {"id": "ID", "vehicleType": "Type de Véhicule", "vehicleTypes": "Types de Véhicules", "season": "<PERSON><PERSON>", "seasons": "Saisons", "pricePerKm": "Prix par KM", "defaultPrice": "Prix par Défaut", "status": "Statut", "actions": "Actions", "difference": "<PERSON>ff<PERSON><PERSON><PERSON>"}, "placeholders": {"selectVehicleType": "Sélectionnez un type de véhicule", "selectVehicleTypes": "Sélectionnez des types de véhicules", "selectSeason": "Sélectionnez une saison", "selectSeasons": "Sélectionnez des saisons", "enterPrice": "Entrez le prix par kilomètre", "enterDefaultPrice": "Entrez le prix par défaut par kilomètre"}, "errors": {"vehicleTypeRequired": "Le type de véhicule est requis", "seasonRequired": "La saison est requise", "priceRequired": "Le prix est requis", "pricePositive": "Le prix doit être un nombre positif", "selectVehicleAndSeason": "Veuillez sélectionner au moins un type de véhicule et une saison"}}, "manage_typeVehicules": {"title": "Types véhicules", "add": "Ajouter un type de véhicule", "edit": "Modifier le type de véhicule", "details": "Détails du type de véhicule", "confirmAction": "Confirmation", "confirmAdd": "Voulez-vous vraiment ajouter ce type de véhicule ?", "confirmUpdate": "Voulez-vous vraiment mettre à jour ce type de véhicule ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce type de véhicule ?", "yes": "O<PERSON>", "no": "Non", "sections": {"basicInfo": "Informations de Base"}, "labels": {"id": "ID", "code": "Code", "name": "Désignation", "name_fr": "Désignation en Français", "name_en": "Désignation en Anglais", "name_ar": "Désignation en Arabe", "capacity": "Capacité", "status": "Statut", "createdAt": "<PERSON><PERSON><PERSON> le", "actions": "Actions"}, "placeholders": {"code": "Entrez le code du type de véhicule", "name": "Entrez le nom du type de véhicule", "name_fr": "Entrez le nom en français", "name_en": "Entrez le nom en anglais", "name_ar": "Entrez le nom en arabe", "capacity": "Entrez la capacité"}, "errors": {"codeRequired": "Le code est requis", "nameRequired": "Le nom est requis", "capacityRequired": "La capacité est requise", "nameFrRequired": "La désignation en français est requise", "nameEnRequired": "La désignation en anglais est requise", "nameArRequired": "La désignation en arabe est requise"}}, "manage_locationTypes": {"title": "Types locations ", "add": "Ajouter un type de location", "edit": "Modifier le type de location", "details": "Détails du type de location", "confirmAction": "Confirmation", "confirmAdd": "Voulez-vous vraiment ajouter ce type de location ?", "confirmUpdate": "Voulez-vous vraiment mettre à jour ce type de location ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce type de location ?", "yes": "O<PERSON>", "no": "Non", "sections": {"basicInfo": "Informations de Base"}, "labels": {"id": "ID", "name": "Désignation", "name_fr": "Désignation en Français", "name_en": "Désignation en Anglais", "name_ar": "Désignation en Arabe", "code": "Code", "documents": "Documents Requis", "status": "Statut", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "Actions"}, "placeholders": {"name_fr": "Entrez le nom en français", "name_en": "Entrez le nom en anglais", "name_ar": "Entrez le nom en arabe", "code": "Entrez le code du type de location", "documents": "Entrez les documents requis"}, "errors": {"nameRequired": "Le nom en français est requis", "codeRequired": "Le code est requis", "nameFrRequired": "La désignation en français est requise", "nameEnRequired": "La désignation en anglais est requise", "nameArRequired": "La désignation en arabe est requise"}}, "manage_typeVehicleTypeLocations": {"title": "Véhicules par location", "add": "Ajouter une relation km", "edit": "Modifier la Relation", "details": "<PERSON><PERSON><PERSON> de la relation", "confirmAction": "Confirmation", "confirmAdd": "Voulez-vous vraiment ajouter cette relation ?", "confirmUpdate": "Voulez-vous vraiment mettre à jour cette relation ?", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette relation ?", "yes": "O<PERSON>", "no": "Non", "minKmMatrix": "<PERSON><PERSON>tres Minimum", "selectVehicleAndLocationType": "Veuillez sélectionner des types de véhicules et des types de location pour afficher la matrice", "labels": {"id": "ID", "vehicleType": "Type de Véhicule", "vehicleTypes": "Types de Véhicules", "locationType": "Type de Localisation", "locationTypes": "Types de Localisation", "minKm": "KM Minimum", "defaultMinKm": "KM Minimum par Défaut", "status": "Statut", "actions": "Actions"}, "placeholders": {"selectVehicleType": "Sélectionnez un type de véhicule", "selectVehicleTypes": "Sélectionnez des types de véhicules", "selectLocationType": "Sélectionnez un type de location", "selectLocationTypes": "Sélectionnez des types de location", "enterMinKm": "Entrez les kilomètres minimum", "enterDefaultMinKm": "Entrez les kilomètres minimum par défaut"}, "errors": {"vehicleTypeRequired": "Le type de véhicule est requis", "locationTypeRequired": "Le type de location est requis", "minKmRequired": "Le KM minimum est requis", "minKmPositive": "Le KM minimum doit être un nombre positif", "selectVehicleAndLocationType": "Veuillez sélectionner au moins un type de véhicule et un type de location"}}, "manage_stats": {"title": "Statistiques", "placeholder": "Sélectionnez une option", "paragraph": "Veuillez choisir un type de statistique", "btn": "appliquer", "reset": "Réinitialiser", "abonnees_trajet": "Abonnés par trajet", "abonnees_ligne": "Abonnés par ligne", "type_abn": "Type d'abonnement", "start_date": "Date de debut", "end_date": "Date de fin", "period": "<PERSON><PERSON><PERSON><PERSON> de vente", "trajet": "<PERSON><PERSON><PERSON>", "governorate": "Gouvernorat", "revenues_governorate": "Revenues par gouvernorat", "nb_abonnees": "Nombre d'abonnées", "subscription": "Abonnement", "sub_count": "Nombre d'abonnés", "sales_periods_compaign": "Périodes de vente par campagne", "montant": "<PERSON><PERSON>", "sub_etab": "Abonnées par etablissement", "sales_periods": "<PERSON><PERSON><PERSON><PERSON> de vente", "etab": "Etablissement", "subscribersCount": "Nombre d'abonnées", "detail_abn_etab": "Détails des abonnées par etablissement", "total_abonnees": "Total Abonnés", "total_montant": "Montant Total", "total_trajets": "Total Trajets", "moyenne_trajet": "Moyenne par Trajet", "donnees_detaillees": "<PERSON><PERSON><PERSON>", "total_etablissements": "Total Établissements", "moyenne_etablissement": "Moyenne par Établissement", "tranch_km": "Tranche Kilométrique", "sub_trancheKm_ligne": "Abonnés par Tranche Kilométrique des Lignes", "ligne": "Ligne", "ligne_tranch_km": "Ligne - Tranche Kilométrique", "total_lignes": "Total Lignes", "moyenne_ligne": "Moyenne par Ligne", "subscriptionNumber": "N° Abonnement", "fullName": "Nom & Prénom", "abn_km": "Abonnés par tranche kilométrique", "abn_remise": "Abonnés par remise", "count": "Nombre", "discount": "Remise", "discountValidity": "Validité de la remise", "comp_recette_sales_period": "Comparatif des Revenues par période de vente", "comparatif_recette": "Comparatif des Revenues", "campaign": "Campagne", "salePeriod": "<PERSON><PERSON><PERSON><PERSON> de vente", "comparatif_recette_agence": "Comparatif des Revenues par agence", "comparatif_recette_ligne_exploitation": "Comparatif des Revenues par lignes d'exploitation", "period_vente_campagne": "Période de vente par campagne", "vente_gouv": "Ventes par gouvernorat", "delegation": "Délegation", "list_sales_periods": "Liste de vente par mode de règlement", "agency": "Agence", "cardFee": "Montant frais carte", "subscriptionAmount": "Montant abonnement", "paymentMethod": "Mode de règlement", "saleDate": "Date de vente", "fiche_conv": "Fiche convention", "client": "Client", "conventionNumber": "N° convention", "subscriptionsCount": "Nombre d'abonnements", "abonnement_ligne_exploitation": "Liste des abonnements par lignes d'exploitation", "salePoint": "Point de vente", "operationLine": "Ligne d'exploitation", "affectation_cartes": "Affectation des cartes par agent de vente", "startSequence": "N° début de séquence", "endSequence": "N° fin de séquence", "cardsCount": "Nombre de cartes", "etablissement": "Établissement", "line": "Ligne", "startDate": "Date de debut", "endDate": "Date de fin", "sales_date": "Date de vente", "agentVente": "Agent de vente", "abonnees_remise": "Abonnés par remise", "remise": "Remise", "nb_abonnements": "Nombre d'abonnements", "total_remises": "Total Remises", "moyenne_remise": "Moyenne par Remise", "recettes_periode_vente": "Revenues par période de vente", "campagne": "Campagne", "periode_vente": "<PERSON><PERSON><PERSON><PERSON> de vente", "nb_transactions": "Nombre de transactions", "total_periodes": "Total Périodes", "moyenne_periode": "Moyenne par Période", "recettes_agence": "Revenues par agence", "agence": "Agence", "total_agences": "Total Agences", "moyenne_agence": "Moyenne par Agence", "recettes_gouvernorat": "Revenus par gouvernorat", "gouvernorat": "Gouvernorat", "total_gouvernorats": "Total Gouvernorats", "moyenne_gouvernorat": "Moyenne par Gouvernorat", "recettes_methode_paiement": "Revenus par méthode de paiement", "methode_paiement": "Méthode de paiement", "total_methodes_paiement": "Total Méthodes de paiement", "moyenne_methode_paiement": "Moyenne par Méthode de paiement"}}